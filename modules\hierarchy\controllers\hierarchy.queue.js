const { Queue, Worker } = require("bullmq");
const { connection } = require('../../../utils/queues/redis');
const path = require('path');

// Create BullMQ Queue for Hierarchy Generation
const queueName = "hierarchyQueue";
const hierarchyQueue = new Queue(queueName, {
  connection,
  defaultJobOptions: { 
    removeOnComplete: 10,     // Keep last 10 completed jobs for monitoring
    removeOnFail: 20,         // Keep last 20 failed jobs for debugging
    attempts: 2,              // Retry failed jobs up to 2 times
    backoff: {
      type: 'exponential',
      delay: 5000,            // Start with 5 second delay
    },
    delay: 0,                 // No initial delay
  },
});

/**
 * Add hierarchy generation job to queue
 * @param {string} miqaatID - Miqaat ID
 * @param {string} arazCityID - ArazCity ID
 * @param {Object} options - Additional job options
 * @returns {Promise<Job>} - BullMQ Job instance
 */
const addHierarchyGenerationJob = async (miqaatID, arazCityID, options = {}) => {
  const jobData = {
    miqaatID,
    arazCityID,
    timestamp: new Date().toISOString(),
  };

  const jobOptions = {
    jobId: `hierarchy-${miqaatID}-${arazCityID}`, // Unique job ID to prevent duplicates
    ...options
  };

  try {
    const job = await hierarchyQueue.add('generateHierarchy', jobData, jobOptions);
    console.log(`Hierarchy generation job added to queue: ${job.id}`);
    return job;
  } catch (error) {
    console.error('Failed to add hierarchy generation job to queue:', error);
    throw error;
  }
};

/**
 * Process hierarchy generation in queue (non-blocking)
 * This is the main function to call instead of generateHierarchy directly
 * @param {string} miqaatID - Miqaat ID
 * @param {string} arazCityID - ArazCity ID
 * @returns {Promise<Object>} - Job information
 */
const processHierarchyGeneration = async (miqaatID, arazCityID) => {
  try {
    const job = await addHierarchyGenerationJob(miqaatID, arazCityID);
    
    return {
      success: true,
      jobId: job.id,
      status: 'queued',
      message: 'Hierarchy generation queued successfully'
    };
  } catch (error) {
    console.error('Failed to process hierarchy generation:', error);
    return {
      success: false,
      error: error.message,
      message: 'Failed to queue hierarchy generation'
    };
  }
};

/**
 * Get job status by job ID
 * @param {string} jobId - Job ID
 * @returns {Promise<Object>} - Job status information
 */
const getJobStatus = async (jobId) => {
  try {
    const job = await hierarchyQueue.getJob(jobId);
    if (!job) {
      return { status: 'not_found', message: 'Job not found' };
    }

    const state = await job.getState();
    const progress = job.progress;
    
    return {
      status: state,
      progress,
      data: job.data,
      processedOn: job.processedOn,
      finishedOn: job.finishedOn,
      failedReason: job.failedReason,
      returnvalue: job.returnvalue,
    };
  } catch (error) {
    console.error('Failed to get job status:', error);
    throw error;
  }
};

/**
 * Get queue statistics
 * @returns {Promise<Object>} - Queue statistics
 */
const getQueueStats = async () => {
  try {
    const waiting = await hierarchyQueue.getWaiting();
    const active = await hierarchyQueue.getActive();
    const completed = await hierarchyQueue.getCompleted();
    const failed = await hierarchyQueue.getFailed();

    return {
      waiting: waiting.length,
      active: active.length,
      completed: completed.length,
      failed: failed.length,
      total: waiting.length + active.length + completed.length + failed.length
    };
  } catch (error) {
    console.error('Failed to get queue stats:', error);
    throw error;
  }
};

/**
 * Cancel a job by ID
 * @param {string} jobId - Job ID to cancel
 * @returns {Promise<boolean>} - Success status
 */
const cancelJob = async (jobId) => {
  try {
    const job = await hierarchyQueue.getJob(jobId);
    if (!job) {
      return false;
    }

    await job.remove();
    console.log(`Job ${jobId} cancelled successfully`);
    return true;
  } catch (error) {
    console.error('Failed to cancel job:', error);
    throw error;
  }
};

/**
 * Clean old jobs from queue
 * @param {number} grace - Grace period in milliseconds
 * @returns {Promise<void>}
 */
const cleanQueue = async (grace = 24 * 60 * 60 * 1000) => { // 24 hours default
  try {
    await hierarchyQueue.clean(grace, 100, 'completed');
    await hierarchyQueue.clean(grace, 100, 'failed');
    console.log('Queue cleaned successfully');
  } catch (error) {
    console.error('Failed to clean queue:', error);
    throw error;
  }
};

// Create and start the worker
const workerFile = path.join(__dirname, '../../../utils/queues/hierarchyWorker.js');
const worker = new Worker(queueName, workerFile, {
  connection,
  concurrency: 1, // Process one hierarchy generation at a time to avoid resource conflicts
  maxStalledCount: 1, // Maximum number of times a job can be stalled before being failed
  stalledInterval: 30 * 1000, // Check for stalled jobs every 30 seconds
  maxMemoryUsage: 1024 * 1024 * 1024, // 1GB memory limit
});

// Worker event handlers
worker.on('ready', () => {
  console.log('Hierarchy worker is ready and waiting for jobs');
});

worker.on('active', (job) => {
  console.log(`Hierarchy generation started for job ${job.id}: miqaatID=${job.data.miqaatID}, arazCityID=${job.data.arazCityID}`);
});

worker.on('completed', (job, result) => {
  console.log(`Hierarchy generation completed for job ${job.id} in ${Date.now() - job.processedOn}ms`);
});

worker.on('failed', (job, err) => {
  console.error(`Hierarchy generation failed for job ${job?.id}:`, err.message);
});

worker.on('stalled', (jobId) => {
  console.warn(`Hierarchy generation job ${jobId} stalled`);
});

worker.on('error', (err) => {
  console.error('Hierarchy worker error:', err);
});

// Graceful shutdown
process.on('SIGINT', async () => {
  console.log('Shutting down hierarchy worker...');
  await worker.close();
  await hierarchyQueue.close();
  process.exit(0);
});

module.exports = {
  hierarchyQueue,
  worker,
  processHierarchyGeneration,
  addHierarchyGenerationJob,
  getJobStatus,
  getQueueStats,
  cancelJob,
  cleanQueue,
};
