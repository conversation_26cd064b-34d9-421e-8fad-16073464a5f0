const {
  api<PERSON><PERSON><PERSON>,
  api<PERSON><PERSON>r,
  apiResponse,
} = require("../../../utils/api.util");
const {
  FETCH,
  ADD_SUCCESS,
  NOT_FOUND,
  UPDATE_SUCCESS,
  DELETE_SUCCESS,
  EXISTS,
  CUSTOM_ERROR,
} = require("../../../utils/message.util");
const { Function, Department } = require("../../hierarchy/models");
const {
  isEmpty,
  getUniqueName,
  toObjectId,
} = require("../../../utils/misc.util");
const {
  redisCacheKeys,
  getCache,
  setCache,
  clearCacheByPattern,
} = require("../../../utils/redis.cache");
const { addEventLog, EventActions, Modules } = require("../../../utils/eventLogs.util");
// const getAllFunctions = apiHandler(async (req, res) => {
//   const functions = await Function.find({}).sort({ _id: -1 });

//   return apiResponse(FETCH, "Functions", functions, res);
// });

const getAllFunctions = apiHandler(async (req, res) => {
  const cacheKey = `${redisCacheKeys.GLOBAL_MASTER}:${redisCacheKeys.FUNCTIONS}:{all}`;
  let data = await getCache(cacheKey);
  if (!isEmpty(data)) {
    return apiResponse(FETCH, "Functions", data, res, true);
  }

  const functions = await Function.aggregate([
    {
      $lookup: {
        from: "departments",
        let: { functionId: "$_id" },
        pipeline: [
          {
            $match: {
              $expr: { $in: ["$$functionId", "$functions"] },
            },
          },
          {
            $project: { name: 1, _id: 1 },
          },
        ],
        as: "departments",
      },
    },
    { $sort: { _id: -1 } },
  ]);
  apiResponse(FETCH, "Functions with departments", functions, res);
  await setCache(cacheKey, functions);
});

const addFunction = apiHandler(async (req, res) => {
  const { name, priority } = req.body;

  const uniqueName = getUniqueName(name);

  const existingFunction = await Function.findOne({ uniqueName });
  if (!isEmpty(existingFunction)) {
    return apiError(
      CUSTOM_ERROR,
      "Function with this name already exists",
      null,
      res
    );
  }

  const newFunction = new Function({
    name,
    uniqueName,
    priority,
    createdBy: req.user._id,
  });

  await newFunction.save();
  clearCacheByPattern(
    `${redisCacheKeys.GLOBAL_MASTER}:${redisCacheKeys.FUNCTIONS}`
  );
  await addEventLog(
    EventActions.CREATE,
    Modules.GlobalMasters,
    "Function",
    req.user._id
  );

  return apiResponse(ADD_SUCCESS, "Function", null, res);
});

const getSingleFunction = apiHandler(async (req, res) => {
  const { id } = req.params;

  const functionData = await Function.findOne({ _id: id });

  if (isEmpty(functionData)) {
    return apiError(NOT_FOUND, "Function", null, res);
  }
  return apiResponse(FETCH, "Function", functionData, res);
});

const editFunction = apiHandler(async (req, res) => {
  const { id, name, priority } = req.body;

  const uniqueName = getUniqueName(name);

  const existingFunction = await Function.findOne({
    uniqueName,
    _id: { $ne: id },
  });

  if (!isEmpty(existingFunction)) {
    return apiError(
      CUSTOM_ERROR,
      "Function with this name already exists",
      null,
      res
    );
  }

  const updateData = {
    name,
    uniqueName,
    priority,
    updatedBy: req.user._id,
  };

  const functionData = await Function.findByIdAndUpdate(id, updateData, {
    new: true,
    runValidators: true,
  });

  if (isEmpty(functionData)) {
    return apiError(NOT_FOUND, "Function", null, res);
  }
  await clearCacheByPattern(
    `${redisCacheKeys.GLOBAL_MASTER}:${redisCacheKeys.FUNCTIONS}`
  );
  
  await addEventLog(
    EventActions.UPDATE,
    Modules.GlobalMasters,
    "Function",
    req.user._id
  );

  return apiResponse(UPDATE_SUCCESS, "Function", functionData, res);
});

const deleteFunction = apiHandler(async (req, res) => {
  const { id } = req.params;

  const existsInDepartment = await Department.exists({
    functions: toObjectId(id),
  });

  if (existsInDepartment) {
    return apiError(
      CUSTOM_ERROR,
      "Couldn't Delete Function as being used in Department",
      null,
      res
    );
  }

  const functionData = await Function.findOneAndDelete({ _id: id });

  if (isEmpty(functionData)) {
    return apiError(NOT_FOUND, "Function", null, res);
  }
clearCacheByPattern(
    `${redisCacheKeys.GLOBAL_MASTER}:${redisCacheKeys.FUNCTIONS}`
  );
  
  await addEventLog(
    EventActions.DELETE,
    Modules.GlobalMasters,
    "Function",
    req.user._id
  );

  return apiResponse(DELETE_SUCCESS, "Function", null, res);
});

const getFunctionsByDepartment = apiHandler(async (req, res) => {
  const { id } = req.params;

  const cachekey = `${redisCacheKeys.GLOBAL_MASTER}:${redisCacheKeys.FUNCTIONS}:${id}`;
  let data = await getCache(cachekey);
  if (!isEmpty(data)) {
    return apiResponse(FETCH, "Function", data, res, true);
  }

  const functionData = await Department.findOne({ _id: id })
    .populate("functions", "name")
    .select("functions");

  if (isEmpty(functionData?.functions)) {
    return apiError(NOT_FOUND, "Function", null, res);
  }

  apiResponse(FETCH, "Function", functionData.functions, res);
  await setCache(cachekey, functionData.functions);
});

const getAllDepartmentFunctions = apiHandler(async (req, res) => {

  const cacheKey = `${redisCacheKeys.GLOBAL_MASTER}:${redisCacheKeys.FUNCTIONS}:{Departments}`;
  const functionData = await Department.find()
    .populate("functions", "name")
    .select("name functions _id");

  if (isEmpty(functionData)) {
    return apiError(NOT_FOUND, "Function", null, res);
  }

  apiResponse(FETCH, "Function", functionData, res);
  await setCache(cacheKey, functionData);
});

module.exports = {
  getAllFunctions,
  addFunction,
  getSingleFunction,
  editFunction,
  deleteFunction,
  getFunctionsByDepartment,
  getAllDepartmentFunctions,
};
