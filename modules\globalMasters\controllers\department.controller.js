const {
  api<PERSON><PERSON><PERSON>,
  api<PERSON>rror,
  apiResponse,
} = require("../../../utils/api.util");
const {
  FETCH,
  ADD_SUCCESS,
  NOT_FOUND,
  UPDATE_SUCCESS,
  DELETE_SUCCESS,
  EXISTS,
  CUSTOM_ERROR,
} = require("../../../utils/message.util");
const {
  Department,
  ArazCity,
  KGUser,
  ArazCityZone,
} = require("../../hierarchy/models");
const {
  isEmpty,
  toObjectId,
  getUniqueName,
} = require("../../../utils/misc.util");
const {
  generateHierarchy,
} = require("../../hierarchy/controllers/hierarchy.controller");
const constants = require("../../../constants");
const {
  redisCacheKeys,
  setCache,
  getCache,
  clearCacheByPattern,
} = require("../../../utils/redis.cache");
const {
  addEventLog,
  EventActions,
  Modules,
} = require("../../../utils/eventLogs.util");
const {
  isActiveMiqaatAndArazCity,
} = require("../../../utils/checkActiveMiqaatAndArazcityStatus");

const getAllDepartments = apiHandler(async (req, res) => {
  const cachekey = `${redisCacheKeys.GLOBAL_MASTER}:${redisCacheKeys.DEPARTMENTS}`;

  let data = await getCache(cachekey);
  if (!isEmpty(data)) {
    return apiResponse(FETCH, "Departments", data, res, true);
  }
  const departments = await Department.find({}).sort({ _id: -1 });
  setCache(cachekey, departments);
  return apiResponse(FETCH, "Departments", departments, res);
});

const addDepartment = apiHandler(async (req, res) => {
  const {
    name,
    LDName,
    status,
    priority,
    isZonal,
    allowDeactivate,
    hierarchyPositions,
    functions,
    cityRoleID,
  } = req.body;

  const uniqueName = getUniqueName(name);

  const existingDepartment = await Department.findOne({
    $or: [{ uniqueName }, LDName ? { LDName } : null, { name }].filter(Boolean),
  });

  if (!isEmpty(existingDepartment)) {
    return apiError(
      CUSTOM_ERROR,
      "Department with this name already exists",
      null,
      res
    );
  }

  const newDepartment = new Department({
    name,
    LDName,
    uniqueName,
    isZonal,
    priority,
    status,
    allowDeactivate,
    hierarchyPositions,
    functions,
    cityRoleID,
    createdBy: req.user._id,
  });
  
  const departmentData = await newDepartment.save();
  
  await ArazCity.updateMany(
    {},
    {
      $push: {
        departments: {
          departmentID: departmentData._id,
          status: departmentData.status,
          hierarchyPositions: departmentData.hierarchyPositions,
        },
      },
    }
  );
  
  await addEventLog(
    EventActions.CREATE,
    Modules.GlobalMasters,
    "Department",
    req.user._id
  );
  
  clearCacheByPattern(redisCacheKeys.GLOBAL_MASTER);
  apiResponse(ADD_SUCCESS, "Department", departmentData, res);

  const arazCityData = await ArazCity.find({ status: true }, "miqaatID");

  await Promise.all(
    arazCityData.map(async (city) => {
      await generateHierarchy(city.miqaatID, city._id);
    })
  );
  await addEventLog(
    EventActions.UPDATE,
    Modules.GlobalMasters,
    "Hierarchy for Department",
    constants.AMS_SYSTEMID
  );

});

const getSingleDepartment = apiHandler(async (req, res) => {
  const { id } = req.params;

  const cachekey = `${redisCacheKeys.GLOBAL_MASTER}:${redisCacheKeys.DEPARTMENTS}:${id}`;
  let data = await getCache(cachekey);
  if (!isEmpty(data)) {
    return apiResponse(FETCH, "Department", data, res, true);
  }

  const departmentData = await Department.findOne({ _id: id });

  if (isEmpty(departmentData)) {
    return apiError(NOT_FOUND, "Department", null, res);
  }

  setCache(cachekey, departmentData);
  return apiResponse(FETCH, "Department", departmentData, res);
});

const editDepartment = apiHandler(async (req, res) => {
  const {
    id,
    name,
    status,
    LDName,
    isZonal,
    priority,
    allowDeactivate,
    hierarchyPositions,
    functions,
    cityRoleID,
  } = req.body;

  const uniqueName = getUniqueName(name);

  const existingDepartment = await Department.findOne({
    $or: [{ uniqueName }, LDName ? { LDName } : null, { name }].filter(Boolean),
    _id: { $ne: id },
  });

  if (!isEmpty(existingDepartment)) {
    return apiError(
      CUSTOM_ERROR,
      "Department with this name already exists",
      null,
      res
    );
  }

  const updateData = {
    name,
    LDName,
    uniqueName,
    isZonal,
    status,
    priority,
    allowDeactivate,
    hierarchyPositions,
    functions,
    cityRoleID,
    updatedBy: req.user._id,
  };

  const departmentData = await Department.findByIdAndUpdate(id, updateData, {
    new: true,
  });

  if (isEmpty(departmentData)) {
    return apiError(NOT_FOUND, "Department", null, res);
  }

  await ArazCity.updateMany(
    { "departments.departmentID": departmentData._id },
    {
      $set: {
        "departments.$.hierarchyPositions": departmentData.hierarchyPositions,
        "departments.$.isZonal": departmentData.isZonal,
      },
    }
  );
  await addEventLog(
    EventActions.UPDATE,
    Modules.GlobalMasters,
    "Department",
    req.user._id
  );
  apiResponse(UPDATE_SUCCESS, "Department", departmentData, res);

  const arazCityData = await ArazCity.find({}, "miqaatID").lean();

  await Promise.all(
    arazCityData.map(async (city) => {
      await generateHierarchy(city.miqaatID, city._id);
    })
  );
  await addEventLog(
    EventActions.UPDATE,
    Modules.GlobalMasters,
    "Hierarchy for Department",
    constants.AMS_SYSTEMID
  );
  clearCacheByPattern(redisCacheKeys.GLOBAL_MASTER);
});

const deleteDepartment = apiHandler(async (req, res) => {
  const { id } = req.params;

  const departmentData = await Department.findOneAndDelete({ _id: id });

  if (isEmpty(departmentData)) {
    return apiError(NOT_FOUND, "Department", null, res);
  }

  await ArazCity.updateMany(
    { "departments.departmentID": id },
    {
      $pull: { departments: { departmentID: id } },
    }
  );

  await addEventLog(
    EventActions.DELETE,
    Modules.GlobalMasters,
    "Department",
    req.user._id
  );
  apiResponse(DELETE_SUCCESS, "Department", null, res);
  clearCacheByPattern(redisCacheKeys.GLOBAL_MASTER);

  const arazCityData = await ArazCity.find({}, "miqaatID").lean();

  await Promise.all(
    arazCityData.map(async (city) => {
      await generateHierarchy(city.miqaatID, city._id);
    })
  );
  await addEventLog(
    EventActions.UPDATE,
    Modules.GlobalMasters,
    "Hierarchy for Department",
    constants.AMS_SYSTEMID
  );
});

const updateDepartmentStatus = apiHandler(async (req, res) => {
  const { arazCityID, departmentIDs, status } = req.body;
  const checkActiveStatus = await isActiveMiqaatAndArazCity(
    null,
    arazCityID,
    req
  );
  if (!checkActiveStatus) {
    return apiError(CUSTOM_ERROR, "Araz City is not active", null, res);
  }

  const arazCityData = await ArazCity.findByIdAndUpdate(
    { _id: arazCityID },
    {
      $set: {
        "departments.$[elem].status": status,
      },
    },
    {
      new: true,
      lean: true,
      arrayFilters: [{ "elem.departmentID": { $in: departmentIDs } }],
    }
  );
  await addEventLog(
    EventActions.UPDATE,
    Modules.GlobalMasters,
    "Department",
    req.user._id
  );
  apiResponse(UPDATE_SUCCESS, "Departments Status", null, res);

  if (arazCityData?.miqaatID) {
    await addEventLog(
      EventActions.UPDATE,
      Modules.GlobalMasters,
      "Hierarchy for Department",
      constants.AMS_SYSTEMID
    );
    await generateHierarchy(arazCityData.miqaatID, arazCityID);
  }

  clearCacheByPattern(redisCacheKeys.GLOBAL_MASTER,null, arazCityID);
});

const updateDepartmentKhidmatStatus = apiHandler(async (req, res) => {
  const { arazCityID, departmentID, showForKhidmatInterest } = req.body;
  const checkActiveStatus = await isActiveMiqaatAndArazCity(
    null,
    arazCityID,
    req
  );
  if (!checkActiveStatus) {
    return apiError(CUSTOM_ERROR, "Araz City is not active", null, res);
  }

  const arazCityData = await ArazCity.findByIdAndUpdate(
    { _id: arazCityID },
    {
      $set: {
        "departments.$[elem].showForKhidmatInterest": showForKhidmatInterest,
      },
    },
    {
      new: true,
      lean: true,
      arrayFilters: [{ "elem.departmentID": departmentID }],
    }
  );
  await addEventLog(
    EventActions.UPDATE,
    Modules.GlobalMasters,
    "Department",
    req.user._id
  );
  await clearCacheByPattern(
    `${redisCacheKeys.HIERARCHY}:*`,
    arazCityData?.miqaatID,
    arazCityID
  );
  apiResponse(UPDATE_SUCCESS, "Departments Status", null, res);

  clearCacheByPattern(redisCacheKeys.GLOBAL_MASTER,null, arazCityID);
});

const getDepartmentByArazCity = apiHandler(async (req, res) => {
  const { id } = req.params;
  const { isActive, showForKhidmatInterest } = req.query;

  const cachekey = `${redisCacheKeys.GLOBAL_MASTER}:${redisCacheKeys.DEPARTMENTS}:${id}:${isActive}:${showForKhidmatInterest}`;
  let data = await getCache(cachekey);
  if (!isEmpty(data)) {
    return apiResponse(FETCH, "Departments", data, res, true);
  }

  const arazCity = await ArazCity.findById(id)
    .select("departments")
    .populate("departments.departmentID");

  if (isEmpty(arazCity)) {
    return apiError(NOT_FOUND, "Araz City", null, res);
  }

  let departments = arazCity.departments
    .map((department) => ({
      id: department.departmentID?._id,
      name: department.departmentID?.name,
      isZonal: department.departmentID?.isZonal,
      allowDeactivate: department.departmentID?.allowDeactivate,
      status: department.status,
      cityRoleID: department.departmentID?.cityRoleID,
      showForKhidmatInterest: department.showForKhidmatInterest,
    }))
    .filter((dept) => dept.id && dept.name);

  if (isActive === "true") {
    departments = departments.filter((dept) => dept.status === "active");
  }

  if (showForKhidmatInterest === "true") {
    departments = departments.filter(
      (dept) => dept.showForKhidmatInterest === true
    );
  }

  if (isEmpty(departments)) {
    return apiError(NOT_FOUND, "No departments found", null, res);
  }

  setCache(cachekey, departments);

  return apiResponse(FETCH, "Departments", departments, res);
});

const addUserToHod = (
  user,
  hods,
  validKgTypes,
  allowPhone,
  allowFemalePhoto
) => {
  const { foundMiqaat } = user;

  if (
    foundMiqaat?.hierarchyPosition?.id.toString() ===
      constants.HIERARCHY_POSITIONS.HOD[0].toString() &&
    validKgTypes.includes(foundMiqaat?.kgType?.uniqueName)
  ) {
    const newHod = {
      position:
        foundMiqaat?.kgType.uniqueName === "local_kg"
          ? foundMiqaat.arazCity.showPositionAlias
            ? foundMiqaat?.hierarchyPosition.alias
            : foundMiqaat?.hierarchyPosition.name
          : foundMiqaat.kgType.name,
      users: [
        {
          name: user.name,
          LDName: user.LDName,
          logo:
            user.gender === "M"
              ? user.logo
              : allowFemalePhoto
              ? user.logo
              : undefined,
          ITSID: user?.ITSID,
          gender: user?.gender,
          priority: foundMiqaat?.kgType?.priority,
          position:
            foundMiqaat?.kgType.uniqueName === "local_kg"
              ? foundMiqaat.arazCity.showPositionAlias
                ? foundMiqaat?.hierarchyPosition.alias
                : foundMiqaat?.hierarchyPosition.name
              : foundMiqaat.kgType.name,
          ...(user.gender === "M"
            ? { phone: user.phone, whatsapp: user.whatsapp }
            : allowPhone
            ? { phone: user.phone, whatsapp: user.whatsapp }
            : {}),
        },
      ],
      priority: foundMiqaat?.kgType?.priority,
      bgColor: foundMiqaat?.kgType?.color,
    };

    const existingHod = hods.find((hod) => hod.position === newHod.position);

    if (existingHod) {
      existingHod.users.push({
        name: user.name,
        LDName: user.LDName,
        logo:
          user.gender === "M"
            ? user.logo
            : allowFemalePhoto
            ? user.logo
            : undefined,
        ITSID: user?.ITSID,
        gender: user?.gender,
        priority: foundMiqaat?.kgType?.priority,
        position:
          foundMiqaat?.kgType.uniqueName === "local_kg"
            ? foundMiqaat.arazCity.showPositionAlias
              ? foundMiqaat?.hierarchyPosition.alias
              : foundMiqaat?.hierarchyPosition.name
            : foundMiqaat.kgType.name,
        ...(user.gender === "M"
          ? { phone: user.phone, whatsapp: user.whatsapp }
          : allowPhone
          ? { phone: user.phone, whatsapp: user.whatsapp }
          : {}),
      });
    } else {
      hods.push(newHod);
    }
  }
};

const addUserToZone = (
  user,
  zonal,
  validKgTypes,
  allowPhone,
  allowFemalePhoto
) => {
  const { foundMiqaat } = user;
  if (
    foundMiqaat?.hierarchyPosition?.id.toString() ===
      constants.HIERARCHY_POSITIONS.ZONE_LEAD[0].toString() &&
    validKgTypes.includes(foundMiqaat?.kgType?.uniqueName)
  ) {
    const existingZone = zonal.find(
      (zone) => zone.zoneName === foundMiqaat?.arazCityZone?.name
    );

    if (existingZone) {
      existingZone.users.push({
        name: user.name,
        LDName: user.LDName,
        logo:
          user.gender === "M"
            ? user.logo
            : allowFemalePhoto
            ? user.logo
            : undefined,
        ITSID: user?.ITSID,
        gender: user?.gender,
        priority: foundMiqaat?.kgType?.priority,
        position:
          foundMiqaat?.kgType.uniqueName === "local_kg"
            ? foundMiqaat.arazCity.showPositionAlias
              ? foundMiqaat?.hierarchyPosition.alias
              : foundMiqaat?.hierarchyPosition.name
            : foundMiqaat.kgType.name,
        ...(user.gender === "M"
          ? { phone: user.phone, whatsapp: user.whatsapp }
          : allowPhone
          ? { phone: user.phone, whatsapp: user.whatsapp }
          : {}),
      });
    }
  }
};

const addUserToFunction = (
  user,
  functions,
  validKgTypes,
  allowPhone,
  allowFemalePhoto
) => {
  const { foundMiqaat } = user;
  if (
    foundMiqaat?.hierarchyPosition?.id.toString() ===
      constants.HIERARCHY_POSITIONS.HOD_TEAM[0].toString() &&
    validKgTypes.includes(foundMiqaat?.kgType?.uniqueName)
  ) {
    // If the user has a function, add them to that function
    if (!isEmpty(foundMiqaat?.function)) {
      const existingFunction = functions.find(
        (entry) => entry.funcName === foundMiqaat.function.name
      );

      if (existingFunction) {
        existingFunction.users.push({
          name: user.name,
          LDName: user.LDName,
          logo:
            user.gender === "M"
              ? user.logo
              : allowFemalePhoto
              ? user.logo
              : undefined,
          ITSID: user?.ITSID,
          gender: user?.gender,
          priority: foundMiqaat?.kgType?.priority,
          position:
            foundMiqaat?.kgType.uniqueName === "local_kg"
              ? foundMiqaat.arazCity.showPositionAlias
                ? foundMiqaat?.hierarchyPosition.alias
                : foundMiqaat?.hierarchyPosition.name
              : foundMiqaat.kgType.name,
          ...(user.gender === "M"
            ? { phone: user.phone, whatsapp: user.whatsapp }
            : allowPhone
            ? { phone: user.phone, whatsapp: user.whatsapp }
            : {}),
          otherFunction: foundMiqaat?.otherFunction,
        });
        return;
      }
    }

    const defaultFunction = functions.find(
      (entry) => entry.funcName === "-" || !entry.funcName
    );

    if (defaultFunction) {
      // Add to existing default function
      defaultFunction.users.push({
        name: user.name,
        LDName: user.LDName,
        logo:
          user.gender === "M"
            ? user.logo
            : allowFemalePhoto
            ? user.logo
            : undefined,
        ITSID: user?.ITSID,
        gender: user?.gender,
        priority: foundMiqaat?.kgType?.priority,
        position:
          foundMiqaat?.kgType.uniqueName === "local_kg"
            ? foundMiqaat.arazCity.showPositionAlias
              ? foundMiqaat?.hierarchyPosition.alias
              : foundMiqaat?.hierarchyPosition.name
            : foundMiqaat.kgType.name,
        ...(user.gender === "M"
          ? { phone: user.phone, whatsapp: user.whatsapp }
          : allowPhone
          ? { phone: user.phone, whatsapp: user.whatsapp }
          : {}),
        otherFunction: foundMiqaat?.otherFunction,
      });
    } else {
      // Create a new default function and add the user
      functions.push({
        id: "",
        funcName: "-",
        priority: Number.MAX_SAFE_INTEGER,
        users: [
          {
            name: user.name,
            LDName: user.LDName,
            llogo:
              user.gender === "M"
                ? user.logo
                : allowFemalePhoto
                ? user.logo
                : undefined,
            ITSID: user?.ITSID,
            gender: user?.gender,
            priority: foundMiqaat?.kgType?.priority,
            position:
              foundMiqaat?.kgType.uniqueName === "local_kg"
                ? foundMiqaat.arazCity.showPositionAlias
                  ? foundMiqaat?.hierarchyPosition.alias
                  : foundMiqaat?.hierarchyPosition.name
                : foundMiqaat.kgType.name,
            ...(user.gender === "M"
              ? { phone: user.phone, whatsapp: user.whatsapp }
              : allowPhone
              ? { phone: user.phone, whatsapp: user.whatsapp }
              : {}),
            otherFunction: foundMiqaat?.otherFunction,
          },
        ],
      });
    }
  }
};

const sortUsersByPriorityAndName = (users) => {
  return users.sort((a, b) => {
    if (a.priority !== b.priority) {
      return a.priority - b.priority;
    }
    return (a.name || "").localeCompare(b.name || "");
  });
};

const sortByPriorityAndName = (items, nameField) => {
  const sortedItems = items.sort((a, b) => {
    if (a.priority !== b.priority) {
      return a.priority - b.priority;
    }
    return (a[nameField] || "").localeCompare(b[nameField] || "");
  });

  sortedItems.forEach((item) => {
    if (item.users && Array.isArray(item.users)) {
      item.users = sortUsersByPriorityAndName(item.users);
    }
  });

  return sortedItems;
};

const getDepartmentReport = apiHandler(async (req, res) => {
  const {
    miqaatID,
    arazCityID,
    departmentID,
    kgTypes = [constants.KG_TYPES.LOCAL_KG[1]],
  } = req.body;

  const validKgTypes = Array.isArray(kgTypes) ? kgTypes : [kgTypes];

  const cacheKey = `${redisCacheKeys.GLOBAL_MASTER}:${
    redisCacheKeys.DEPARTMENT_REPORT
  }:${miqaatID}:${arazCityID}:${departmentID}:${validKgTypes
    .sort()
    .join(":")}:${req.allowPhone}`;

  let data = await getCache(cacheKey, miqaatID, arazCityID);
  if (!isEmpty(data)) {
    return apiResponse(FETCH, "Department Report", data, res, true);
  }

  const checkActiveStatus = await isActiveMiqaatAndArazCity(
    miqaatID,
    arazCityID,
    req
  );
  if (!checkActiveStatus) {
    return apiError(CUSTOM_ERROR, "Araz City is not active", null, res);
  }

  let users = await KGUser.aggregate([
    { $unwind: { path: "$miqaats", preserveNullAndEmptyArrays: false } },
    {
      $match: {
        "miqaats.miqaatID": toObjectId(miqaatID),
        "miqaats.arazCityID": toObjectId(arazCityID),
        "miqaats.departmentID": toObjectId(departmentID),
        "miqaats.isActive": true,
      },
    },
    {
      $lookup: {
        from: "arazcityzones",
        localField: "miqaats.arazCityZoneID",
        foreignField: "_id",
        as: "arazCityZone",
      },
    },
    { $unwind: { path: "$arazCityZone", preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: "arazcities",
        localField: "miqaats.arazCityID",
        foreignField: "_id",
        as: "arazCity",
      },
    },
    { $unwind: { path: "$arazCity", preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: "departments",
        localField: "miqaats.departmentID",
        foreignField: "_id",
        as: "department",
      },
    },
    { $unwind: { path: "$department", preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: "functions",
        localField: "miqaats.functionID",
        foreignField: "_id",
        as: "function",
      },
    },
    { $unwind: { path: "$function", preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: "miqaats",
        localField: "miqaats.miqaatID",
        foreignField: "_id",
        as: "miqaat",
      },
    },
    { $unwind: { path: "$miqaat", preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: "hierarchypositions",
        localField: "miqaats.hierarchyPositionID",
        foreignField: "_id",
        as: "hierarchyPosition",
      },
    },
    {
      $unwind: { path: "$hierarchyPosition", preserveNullAndEmptyArrays: true },
    },
    {
      $lookup: {
        from: "kgtypes",
        localField: "miqaats.kgTypeID",
        foreignField: "_id",
        as: "kgType",
      },
    },
    { $unwind: { path: "$kgType", preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: "kggroups",
        localField: "miqaats.kgGroupID",
        foreignField: "_id",
        as: "kgGroup",
      },
    },
    { $unwind: { path: "$kgGroup", preserveNullAndEmptyArrays: true } },
    {
      $addFields: {
        "miqaats.miqaat": {
          id: "$miqaat._id",
          name: "$miqaat.name",
        },
        "miqaats.arazCity": {
          id: "$arazCity._id",
          name: "$arazCity.name",
          showPositionAlias: "$arazCity.showPositionAlias",
        },
        "miqaats.arazCityZone": {
          id: "$arazCityZone._id",
          name: "$arazCityZone.name",
          priority: "$arazCityZone.priority",
        },
        "miqaats.department": {
          id: "$department._id",
          name: "$department.name",
          priority: "$department.priority",
        },
        "miqaats.function": {
          id: "$function._id",
          name: "$function.name",
          priority: "$function.priority",
        },
        "miqaats.kgType": {
          id: "$kgType._id",
          name: "$kgType.name",
          uniqueName: "$kgType.uniqueName",
          priority: "$kgType.priority",
          color: "$kgType.color",
        },
        "miqaats.kgGroup": {
          id: "$kgGroup._id",
          name: "$kgGroup.name",
        },
        "miqaats.hierarchyPosition": {
          id: "$hierarchyPosition._id",
          name: "$hierarchyPosition.name",
          alias: "$hierarchyPosition.alias",
          uniqueName: "$hierarchyPosition.uniqueName",
        },
      },
    },
    {
      $group: {
        _id: "$_id",
        name: { $first: "$name" },
        LDName: { $first: "$LDName" },
        ITSID: { $first: "$ITSID" },
        phone: { $first: "$phone" },
        whatsapp: { $first: "$whatsapp" },
        logo: { $first: "$logo" },
        gender: { $first: "$gender" },
        miqaats: { $push: "$miqaats" },
      },
    },
    {
      $addFields: {
        foundMiqaat: {
          $arrayElemAt: [
            {
              $filter: {
                input: "$miqaats",
                as: "miqaat",
                cond: {
                  $and: [
                    { $eq: ["$$miqaat.miqaat.id", toObjectId(miqaatID)] },
                    { $eq: ["$$miqaat.arazCity.id", toObjectId(arazCityID)] },
                    {
                      $eq: ["$$miqaat.department.id", toObjectId(departmentID)],
                    },
                    { $eq: ["$$miqaat.isActive", true] },
                  ],
                },
              },
            },
            0,
          ],
        },
      },
    },
    {
      $project: {
        _id: 1,
        name: 1,
        LDName: 1,
        logo: 1,
        phone: 1,
        whatsapp: 1,
        ITSID: 1,
        gender: 1,
        foundMiqaat: 1,
      },
    },
  ]);

  if (validKgTypes.length > 0) {
    users = users.filter((user) =>
      validKgTypes.includes(user.foundMiqaat?.kgType?.uniqueName)
    );
  }

  let report = {};
  let hods = [];

  let zonal = [];
  let functions = [
    {
      id: "",
      funcName: "",
      priority: Number.MAX_SAFE_INTEGER,
      users: [],
    },
  ];

  users.sort((a, b) => (a.name || "").localeCompare(b.name || ""));

  let departmentData = await Department.aggregate([
    { $match: { _id: toObjectId(departmentID) } },
    {
      $lookup: {
        from: "functions",
        localField: "functions",
        foreignField: "_id",
        as: "functions",
      },
    },
    {
      $project: {
        isZonal: 1,
        name: 1,
        LDName: 1,
        functions: {
          $map: {
            input: "$functions",
            as: "func",
            in: {
              id: "$$func._id",
              funcName: "$$func.name",
              priority: "$$func.priority",
              users: [],
            },
          },
        },
      },
    },
  ]);

  if (departmentData[0].isZonal) {
    let zones = await ArazCity.aggregate([
      { $match: { _id: toObjectId(arazCityID) } },
      {
        $lookup: {
          from: "arazcityzones",
          localField: "arazCityZones",
          foreignField: "_id",
          as: "arazCityZones",
        },
      },
      {
        $project: {
          arazCityZones: {
            $map: {
              input: "$arazCityZones",
              as: "zone",
              in: {
                id: "$$zone._id",
                zoneName: "$$zone.name",
                zoneLDName: "$$zone.LDName",
                priority: "$$zone.priority",
                users: [],
              },
            },
          },
        },
      },
    ]);
    zonal = zones[0].arazCityZones;
  }

  if (!isEmpty(departmentData[0].functions)) {
    functions = departmentData[0].functions;
  }

  users.forEach((user) => {
    addUserToHod(
      user,
      hods,
      validKgTypes,
      req.allowPhone,
      req.allowFemalePhoto
    );
    addUserToZone(
      user,
      zonal,
      validKgTypes,
      req.allowPhone,
      req.allowFemalePhoto
    );
    addUserToFunction(
      user,
      functions,
      validKgTypes,
      req.allowPhone,
      req.allowFemalePhoto
    );
  });

  report = {
    hod: sortByPriorityAndName(hods, "position"),
    zonal: sortByPriorityAndName(zonal, "zoneName"),
    functions: sortByPriorityAndName(functions, "funcName"),
  };

  apiResponse(FETCH, "Department Report", report, res);

  await setCache(cacheKey, report, miqaatID, arazCityID);
});

const getZoneWisHODReport = apiHandler(async (req, res) => {
  const {
    miqaatID,
    arazCityID,
    kgTypes = [constants.KG_TYPES.LOCAL_KG[1]],
  } = req.body;

  const validKgTypes = Array.isArray(kgTypes) ? kgTypes : [kgTypes];

  const cacheKey = `${redisCacheKeys.HIERARCHY}:${
    redisCacheKeys.ARAZ_CITY_ZONE_REPORT
  }:${miqaatID}:${arazCityID}:${validKgTypes.sort().join(":")}:${
    req.allowPhone
  }`;

  let report = await getCache(cacheKey, miqaatID, arazCityID);
  if (!isEmpty(report)) {
    return apiResponse(FETCH, "Araz City Zone HOD Report", report, res, true);
  }

  const checkActiveStatus = await isActiveMiqaatAndArazCity(
    miqaatID,
    arazCityID,
    req
  );
  if (!checkActiveStatus) {
    return apiError(CUSTOM_ERROR, "Araz City is not active", null, res);
  }

  const arazCity = await ArazCity.findById(arazCityID)
    .populate("arazCityZones")
    .lean();

  if (isEmpty(arazCity)) {
    return apiError(NOT_FOUND, "Araz City not found", null, res);
  }

  const zoneIDs = arazCity.arazCityZones.map((zone) => zone._id);

  let hodsData = await KGUser.aggregate([
    { $unwind: { path: "$miqaats", preserveNullAndEmptyArrays: true } },
    {
      $match: {
        "miqaats.miqaatID": toObjectId(miqaatID),
        "miqaats.arazCityID": toObjectId(arazCityID),
        "miqaats.isActive": true,
        "miqaats.arazCityZoneID": { $in: zoneIDs },
      },
    },
    {
      $lookup: {
        from: "arazcityzones",
        localField: "miqaats.arazCityZoneID",
        foreignField: "_id",
        as: "arazCityZone",
      },
    },
    { $unwind: { path: "$arazCityZone", preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: "departments",
        localField: "miqaats.departmentID",
        foreignField: "_id",
        as: "department",
      },
    },
    { $unwind: { path: "$department", preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: "hierarchypositions",
        localField: "miqaats.hierarchyPositionID",
        foreignField: "_id",
        as: "hierarchyPosition",
      },
    },
    {
      $unwind: { path: "$hierarchyPosition", preserveNullAndEmptyArrays: true },
    },
    {
      $lookup: {
        from: "kgtypes",
        localField: "miqaats.kgTypeID",
        foreignField: "_id",
        as: "kgType",
      },
    },
    { $unwind: { path: "$kgType", preserveNullAndEmptyArrays: true } },
    {
      $match: {
        "hierarchyPosition.uniqueName":
          constants.HIERARCHY_POSITIONS.ZONE_HEAD[1],
        "kgType.uniqueName": {
          $in: validKgTypes,
        },
      },
    },
    {
      $project: {
        name: 1,
        LDName: 1,
        ITSID: 1,
        phone: 1,
        whatsapp: 1,
        logo: 1,
        gender: 1,
        zoneName: "$arazCityZone.name",
        zoneID: "$arazCityZone._id",
        zonePriority: "$arazCityZone.priority",
        departmentName: "$department.name",
        departmentID: "$department._id",
        departmentPriority: "$department.priority",
        hierarchyPositionPriority: "$hierarchyPosition.priority",
        hierarchyPositionName: "$hierarchyPosition.name",
        hierarchyPositionNameAlias: "$hierarchyPosition.alias",
        position: "$kgType.name",
        positionColor: "$kgType.color",
        kgTypeID: "$kgType._id",
        kgTypeUniqueName: "$kgType.uniqueName",
        kgTypePriority: "$kgType.priority",
      },
    },
  ]);
  hodsData = hodsData.filter((hod) => {
    if (validKgTypes.includes(hod.kgTypeUniqueName)) {
      return hod;
    }
  });
  const zoneMap = new Map();
  arazCity.arazCityZones.forEach((zone) => {
    zoneMap.set(zone._id.toString(), {
      zoneID: zone._id,
      zoneName: zone.name,
      LDName: zone?.LDName || "",
      zonePriority: zone.priority || 0,
      hods: [],
    });
  });

  hodsData.forEach((hod) => {
    if (hod.zoneID) {
      const zoneId = hod.zoneID.toString();
      if (zoneMap.has(zoneId)) {
        const zoneData = zoneMap.get(zoneId);
        zoneData.hods.push({
          _id: hod._id,
          name: hod.name,
          LDName: hod.LDName,
          ITSID: hod.ITSID,
          ...(hod.gender === "M"
            ? { phone: hod.phone, whatsapp: hod.whatsapp }
            : req.allowPhone
            ? { phone: hod.phone, whatsapp: hod.whatsapp }
            : {}),
          logo:
            hod.gender === "M"
              ? hod.logo
              : req.allowFemalePhoto
              ? hod.logo
              : undefined,
          departmentName: hod.departmentName,
          departmentID: hod.departmentID,
          position:
            hod.kgTypeUniqueName === "local_kg"
              ? arazCity.showPositionAlias
                ? hod.hierarchyPositionNameAlias
                : hod.hierarchyPositionName
              : hod.position,
          positionColor: hod.positionColor,
          priority: hod.kgTypePriority,
        });
      }
    }
  });

  zoneMap.forEach((zoneData) => {
    zoneData.hods.sort((a, b) => {
      if (a.priority !== b.priority) {
        return a.priority - b.priority;
      }
      return a.name.localeCompare(b.name);
    });
  });

  const result = Array.from(zoneMap.values()).sort(
    (a, b) =>
      a.zonePriority - b.zonePriority || a.zoneName.localeCompare(b.zoneName)
  );

  apiResponse(FETCH, "Zone-wise HOD Report", result, res);

  await setCache(cacheKey, result, miqaatID, arazCityID);
});

const getDepartmentWiseHODReport = apiHandler(async (req, res) => {
  const {
    arazCityID,
    miqaatID,
    kgTypes = [constants.KG_TYPES.LOCAL_KG[1]],
  } = req.body;
  const validKgTypes = Array.isArray(kgTypes) ? kgTypes : [kgTypes];

  const cacheKey = `${redisCacheKeys.HIERARCHY}:${
    redisCacheKeys.CHAIRMAN_REPORT
  }:${miqaatID}:${arazCityID}:${validKgTypes.sort().join(",")}:${
    req.allowPhone
  }`;

  const cachedData = await getCache(cacheKey, miqaatID, arazCityID);
  if (cachedData) {
    return apiResponse(
      FETCH,
      "Department-wise HOD Report",
      cachedData,
      res,
      true
    );
  }

  const checkActiveStatus = await isActiveMiqaatAndArazCity(
    miqaatID,
    arazCityID,
    req
  );
  if (!checkActiveStatus) {
    return apiError(CUSTOM_ERROR, "Araz City is not active", null, res);
  }

  const arazCity = await ArazCity.findById(arazCityID)
    .populate({
      path: "departments.departmentID",
      select: "name LDName priority isZonal showPositionAlias",
    })
    .lean();

  if (isEmpty(arazCity)) {
    return apiError(NOT_FOUND, "Araz City not found", null, res);
  }

  const departmentIDs = arazCity.departments.map(
    (dept) => dept.departmentID._id
  );

  const hods = await KGUser.aggregate([
    { $unwind: { path: "$miqaats", preserveNullAndEmptyArrays: true } },
    {
      $match: {
        "miqaats.miqaatID": toObjectId(miqaatID),
        "miqaats.arazCityID": toObjectId(arazCityID),
        "miqaats.isActive": true,
        "miqaats.departmentID": { $in: departmentIDs },
      },
    },
    {
      $lookup: {
        from: "departments",
        localField: "miqaats.departmentID",
        foreignField: "_id",
        as: "department",
      },
    },
    { $unwind: { path: "$department", preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: "hierarchypositions",
        localField: "miqaats.hierarchyPositionID",
        foreignField: "_id",
        as: "hierarchyPosition",
      },
    },
    {
      $unwind: { path: "$hierarchyPosition", preserveNullAndEmptyArrays: true },
    },
    {
      $lookup: {
        from: "kgtypes",
        localField: "miqaats.kgTypeID",
        foreignField: "_id",
        as: "kgType",
      },
    },
    {
      $unwind: { path: "$kgType", preserveNullAndEmptyArrays: true },
    },
    {
      $match: {
        "hierarchyPosition.uniqueName": constants.HIERARCHY_POSITIONS.HOD[1],
        "kgType.uniqueName": {
          $in: kgTypes,
        },
      },
    },
    {
      $project: {
        name: 1,
        LDName: 1,
        ITSID: 1,
        phone: 1,
        whatsapp: 1,
        logo: 1,
        gender: 1,
        departmentName: "$department.name",
        departmentID: "$department._id",
        departmentPriority: "$department.priority",
        isZonal: "$department.isZonal",
        hierarchyPositionPriority: "$kgType.priority",
        position: "$hierarchyPosition.name",
        positionAlias: "$hierarchyPosition.alias",
        kgType: "$kgType.name",
        kgTypeID: "$kgType._id",
        kgTypeUniqueName: "$kgType.uniqueName",
      },
    },
  ]);

  const hodsByDepartment = {};
  hods.forEach((hod) => {
    const deptId = hod.departmentID.toString();
    if (!hodsByDepartment[deptId]) {
      hodsByDepartment[deptId] = [];
    }

    const showPositionAlias = arazCity.showPositionAlias;
    const displayPosition =
      hod.kgTypeUniqueName === constants.KG_TYPES.LOCAL_KG[1]
        ? showPositionAlias
          ? hod.positionAlias
          : hod.position
        : hod.kgType;

    const hodWithFilteredPhone = {
      ...hod,
      phone: hod.gender === "M" ? hod.phone : req.allowPhone ? hod.phone : null,
      whatsapp:
        hod.gender === "M"
          ? hod.whatsapp
          : req.allowPhone
          ? hod.whatsapp
          : null,
      logo:
        hod.gender === "M"
          ? hod.logo
          : req.allowFemalePhoto
          ? hod.logo
          : undefined,
      position: displayPosition,
    };

    if (validKgTypes.includes(hod?.kgTypeUniqueName)) {
      hodsByDepartment[deptId].push(hodWithFilteredPhone);
    }
  });

  Object.keys(hodsByDepartment).forEach((deptId) => {
    hodsByDepartment[deptId].sort((a, b) => {
      if (a.hierarchyPositionPriority !== b.hierarchyPositionPriority) {
        return a.hierarchyPositionPriority - b.hierarchyPositionPriority;
      }
      return a.name.localeCompare(b.name);
    });
  });

  const departmentsWithHODs = arazCity.departments.map((dept) => {
    const deptId = dept.departmentID._id.toString();
    const departmentHODs = hodsByDepartment[deptId] || [];

    return {
      _id: dept.departmentID._id,
      name: dept.departmentID.name,
      LDName: dept.departmentID?.LDName || "",
      priority: dept.departmentID.priority,
      isZonal: dept.departmentID.isZonal,
      hods: departmentHODs,
    };
  });

  apiResponse(FETCH, "Local Department HODs Report", departmentsWithHODs, res);
  await setCache(cacheKey, departmentsWithHODs, miqaatID, arazCityID);
});

const getDepartmentQuota = apiHandler(async (req, res) => {
  const departmentQuota = await Department.find({}).select(
    "_id name departmentQuota"
  );

  if (isEmpty(departmentQuota)) {
    return apiError(NOT_FOUND, "Department Quota", null, res);
  }

  return apiResponse(FETCH, "Department Quota", departmentQuota, res);
});

const addEditDepartmentQuota = apiHandler(async (req, res) => {
  const { departments } = req.body;

  const promises = departments.map(async (dept) => {
    const { departmentID, quota } = dept;

    const quotaData = await Department.findOneAndUpdate(
      { _id: toObjectId(departmentID) },
      { departmentQuota: quota },
      {
        new: true,
        runValidators: true,
      }
    );

    if (!quotaData) {
      return apiError(NOT_FOUND, "Department Quota", null, res);
    }

    return quotaData;
  });

  const quotaData = await Promise.all(promises);

  return apiResponse(ADD_SUCCESS, "Department Quota", quotaData, res);
});

module.exports = {
  getAllDepartments,
  addDepartment,
  getSingleDepartment,
  editDepartment,
  deleteDepartment,
  updateDepartmentStatus,
  getDepartmentByArazCity,
  getDepartmentReport,
  getZoneWisHODReport,
  getDepartmentWiseHODReport,
  addEditDepartmentQuota,
  getDepartmentQuota,
  updateDepartmentKhidmatStatus,
};
