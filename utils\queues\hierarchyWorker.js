/**
 * Hierarchy Generation Worker
 * This worker processes hierarchy generation jobs in a separate process
 * to avoid blocking the main thread during intensive computations.
 */

const path = require('path');
const { Hierarchy } = require('../../modules/hierarchy/models');

/**
 * Worker job processor
 * This function is called by BullMQ for each job
 */
module.exports = async (job) => {
  const { miqaatID, arazCityID } = job.data;

  console.log(`Starting hierarchy generation for miqaatID: ${miqaatID}, arazCityID: ${arazCityID}`);

  try {
    // Import required modules with proper path resolution
    const { connectDB } = require(path.resolve(__dirname, '../../db'));
    const { generateHierarchyDirect } = require(path.resolve(__dirname, '../../modules/hierarchy/controllers/hierarchy.controller'));

    // Ensure database connection
    const mongoose = require('mongoose');
    if (!mongoose.connection.readyState) {
      await connectDB();
    }

    // Update job progress
    await job.updateProgress(10);

    // Execute hierarchy generation using the direct function
    const result = await generateHierarchyDirect(miqaatID, arazCityID);

    await job.updateProgress(100);

    console.log(`Completed hierarchy generation for miqaatID: ${miqaatID}, arazCityID: ${arazCityID}`);

    return {
      success: true,
      message: 'Hierarchy generated successfully',
      miqaatID,
      arazCityID,
      result
    };

  } catch (error) {
    console.error('Hierarchy generation failed:', error);

    // Try to reset the generating flag on error
    try {
      const mongoose = require('mongoose');

      // Ensure we have a database connection for the error cleanup
      if (!mongoose.connection.readyState) {
        const { connectDB } = require(path.resolve(__dirname, '../../db'));
        await connectDB();
      }

      await Hierarchy.findOneAndUpdate(
        { miqaatID, arazCityID },
        { $set: { isGenerating: false } }
      );

      console.log(`Reset isGenerating flag for miqaatID: ${miqaatID}, arazCityID: ${arazCityID}`);
    } catch (resetError) {
      console.error('Failed to reset generating flag:', resetError);
    }

    throw error;
  }
};
