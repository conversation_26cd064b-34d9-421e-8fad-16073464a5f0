/**
 * Hierarchy Generation Worker
 * This worker processes hierarchy generation jobs in a separate process
 * to avoid blocking the main thread during intensive computations.
 */

const { connectDB } = require('../../db');

/**
 * Worker job processor
 * This function is called by BullMQ for each job
 */
module.exports = async (job) => {
  const { miqaatID, arazCityID } = job.data;
  
  console.log(`Starting hierarchy generation for miqaatID: ${miqaatID}, arazCityID: ${arazCityID}`);
  
  try {
    // Ensure database connection
    if (!require('mongoose').connection.readyState) {
      await connectDB();
    }
    
    // Import the direct hierarchy generation function
    const { generateHierarchyDirect } = require('../../modules/hierarchy/controllers/hierarchy.controller');
    
    // Update job progress
    await job.updateProgress(10);
    
    // Execute hierarchy generation using the direct function
    const result = await generateHierarchyDirect(miqaatID, arazCityID);
    
    await job.updateProgress(100);
    
    console.log(`Completed hierarchy generation for miqaatID: ${miqaatID}, arazCityID: ${arazCityID}`);
    
    return {
      success: true,
      message: 'Hierarchy generated successfully',
      miqaatID,
      arazCityID
    };
    
  } catch (error) {
    console.error('Hierarchy generation failed:', error);
    
    // Try to reset the generating flag on error
    try {
      const { Hierarchy } = require('../../modules/hierarchy/models');
      await Hierarchy.findOneAndUpdate(
        { miqaatID, arazCityID },
        { $set: { isGenerating: false } }
      );
    } catch (resetError) {
      console.error('Failed to reset generating flag:', resetError);
    }
    
    throw error;
  }
};
