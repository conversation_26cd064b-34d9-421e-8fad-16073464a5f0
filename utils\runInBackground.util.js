const { Worker, isMainThread, parentPort, workerData } = require('worker_threads');
const path = require('path');

/**
 * Utility to run any function in a separate worker thread
 * Usage: const fn = runInBg(async (...args) => { ... })
 * Then call: await fn(arg1, arg2, ...)
 */

if (!isMainThread) {
  // Worker thread code
  (async () => {
    try {
      const { functionString, args, requirePaths, dbConfig } = workerData;

      // Setup database connection in worker thread if provided
      if (dbConfig) {
        const mongoose = require('mongoose');
        await mongoose.connect(dbConfig.uri, dbConfig.options || {});
      }

      // Require all necessary modules in worker thread
      const modules = {};
      for (const [name, modulePath] of Object.entries(requirePaths)) {
        try {
          modules[name] = require(modulePath);
        } catch (error) {
          console.warn(`Failed to require module ${name} from ${modulePath}:`, error.message);
        }
      }

      // Recreate the function from string
      const AsyncFunction = Object.getPrototypeOf(async function(){}).constructor;
      const func = new AsyncFunction('modules', 'require', functionString);

      // Execute the function with modules and require available
      const result = await func(modules, require, ...args);

      parentPort.postMessage({ success: true, result });
    } catch (error) {
      parentPort.postMessage({
        success: false,
        error: {
          message: error.message,
          stack: error.stack,
          name: error.name
        }
      });
    }
  })();
} else {
  // Main thread code

  /**
   * Wraps a function to run in a worker thread
   * @param {Function} fn - The async function to run in background
   * @param {Object} options - Configuration options
   * @param {Object} options.modules - Object mapping module names to their paths for worker thread
   * @param {Object} options.dbConfig - Database configuration {uri, options}
   * @returns {Function} - Wrapped function that runs in worker thread
   */
  function runInBg(fn, options = {}) {
    const { modules = {}, dbConfig = null } = options;

    // Default modules that are commonly needed for your project
    const defaultModules = {
      // Core Node.js modules
      path: 'path',
      fs: 'fs',

      // Database
      mongoose: 'mongoose',

      // Your project utilities (adjust paths as needed)
      messageUtil: '../utils/message.util',
      apiUtil: '../utils/api.util',
      miscUtil: '../utils/misc.util',
      redisCache: '../utils/redis.cache',
      eventLogsUtil: '../utils/eventLogs.util',
      openProject: '../utils/openProject',
      checkActiveMiqaatAndArazcityStatus: '../utils/checkActiveMiqaatAndArazcityStatus',

      // Constants
      constants: '../constants'
    };

    const allModules = { ...defaultModules, ...modules };

    return async function(...args) {
      return new Promise((resolve, reject) => {
        // Convert function to string for transfer to worker
        const functionString = fn.toString();

        // Create worker with current file as the worker script
        const worker = new Worker(__filename, {
          workerData: {
            functionString,
            args,
            requirePaths: allModules,
            dbConfig
          }
        });

        // Handle worker completion
        worker.on('message', (data) => {
          worker.terminate();

          if (data.success) {
            resolve(data.result);
          } else {
            const error = new Error(data.error.message);
            error.stack = data.error.stack;
            error.name = data.error.name;
            reject(error);
          }
        });

        // Handle worker errors
        worker.on('error', (error) => {
          worker.terminate();
          reject(error);
        });

        // Handle worker exit
        worker.on('exit', (code) => {
          if (code !== 0) {
            reject(new Error(`Worker stopped with exit code ${code}`));
          }
        });
      });
    };
  }

  /**
   * Specialized wrapper for hierarchy generation with all required dependencies
   * @param {Function} fn - The hierarchy generation function
   * @returns {Function} - Wrapped function that runs in worker thread
   */
  function runHierarchyInBg(fn) {
    const hierarchyModules = {
      // Core Node.js modules
      path: 'path',
      fs: 'fs',

      // Database
      mongoose: 'mongoose',

      // Your project models - using the index file that exports all models
      models: '../modules/hierarchy/models/index',

      // Your project utilities
      messageUtil: '../utils/message.util',
      apiUtil: '../utils/api.util',
      miscUtil: '../utils/misc.util',
      redisCache: '../utils/redis.cache',
      eventLogsUtil: '../utils/eventLogs.util',
      openProject: '../utils/openProject',
      checkActiveMiqaatAndArazcityStatus: '../utils/checkActiveMiqaatAndArazcityStatus',

      // Constants
      constants: '../constants',

      // Controllers (for helper functions)
      positionAssignmentController: '../modules/hierarchy/controllers/positionAssignment.controller'
    };

    // Get database config from environment or mongoose connection
    const dbConfig = process.env.MONGODB_URI ? {
      uri: process.env.MONGODB_URI,
      options: {
        useNewUrlParser: true,
        useUnifiedTopology: true
      }
    } : null;

    return runInBg(fn, { modules: hierarchyModules, dbConfig });
  }

  module.exports = { runInBg, runHierarchyInBg };
}
